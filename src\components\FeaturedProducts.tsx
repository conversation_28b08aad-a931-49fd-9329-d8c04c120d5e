

import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import ProductCard from "./ProductCard";
import { Product } from "@/types";
import { ArrowRight } from "lucide-react";
import { allProducts } from "@/pages/Products";

// Get actual featured products (Goslo premium products) from the main product data
const featuredProducts: Product[] = allProducts.filter(product => product.featured);

const FeaturedProducts = () => {
  return (
    <section className="section bg-cream/50">
      <div className="container-custom">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-end mb-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <span className="inline-block px-3 py-1 bg-mint text-primary text-sm font-medium rounded-full mb-4">Our Premium Selection</span>
            <h2 className="subheadline max-w-lg">Premium Ice Cream</h2>
          </motion.div>

          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Link
              to="/products"
              className="group inline-flex items-center text-primary font-medium mt-4 md:mt-0 hover:underline"
            >
              <span>Explore All Products</span>
              <ArrowRight className="ml-2 w-4 h-4 transition-transform duration-300 group-hover:translate-x-1" />
            </Link>
          </motion.div>
        </div>

        <div className="flex overflow-x-auto pb-4 gap-6 lg:gap-8 hide-scrollbar">
          {featuredProducts.map((product, index) => (
            <div className="flex-shrink-0 w-[280px]" key={product.id}>
              <ProductCard product={product} index={index} />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FeaturedProducts;
