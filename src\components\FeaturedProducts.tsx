

import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import ProductCard from "./ProductCard";
import { Product } from "@/types";
import { ArrowRight } from "lucide-react";

// Static featured products for the homepage (using unique IDs to avoid conflicts)
const featuredProducts: Product[] = [
  {
    id: 101,
    name: "Cookies & Cream",
    description: "Premium vanilla ice cream generously mixed with crushed chocolate cookie pieces, creating an irresistible combination of smooth and crunchy textures in every bite.",
    price: 850,
    image: "https://smartmedia.digital4danone.com//is/image/danonecs/sm_Cookies_cream?wid=480&fmt=png-alpha&fit=wrap",
    category: "Premium",
    featured: true,
  },
  {
    id: 102,
    name: "Peanut Butter",
    description: "Rich and creamy peanut butter ice cream swirled with smooth butter cream, delivering an indulgent nutty flavor that peanut butter enthusiasts will love.",
    price: 850,
    image: "https://smartmedia.digital4danone.com//is/image/danonecs/sm_Peanut_butter?wid=480&fmt=png-alpha&fit=wrap",
    category: "Premium",
    featured: true,
  },
  {
    id: 103,
    name: "Salted Caramel",
    description: "Smooth vanilla ice cream with ribbons of salted caramel throughout, creating a perfect balance of sweet and salty flavors that dance on your palate.",
    price: 850,
    image: "https://smartmedia.digital4danone.com//is/image/danonecs/sm_Salted_caramel?wid=480&fmt=png-alpha&fit=wrap",
    category: "Premium",
    featured: true,
  },
  {
    id: 104,
    name: "Chocolate Almond",
    description: "Decadent chocolate ice cream loaded with roasted almond pieces, offering a luxurious blend of rich cocoa and nutty crunch in every spoonful.",
    price: 850,
    image: "https://smartmedia.digital4danone.com//is/image/danonecs/sm_Chocolate_almond?wid=480&fmt=png-alpha&fit=wrap",
    category: "Premium",
    featured: true,
  },
];

const FeaturedProducts = () => {
  return (
    <section className="section bg-cream/50">
      <div className="container-custom">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-end mb-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <span className="inline-block px-3 py-1 bg-mint text-primary text-sm font-medium rounded-full mb-4">Our Premium Selection</span>
            <h2 className="subheadline max-w-lg">Premium Ice Cream</h2>
          </motion.div>

          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Link
              to="/products"
              className="group inline-flex items-center text-primary font-medium mt-4 md:mt-0 hover:underline"
            >
              <span>Explore All Products</span>
              <ArrowRight className="ml-2 w-4 h-4 transition-transform duration-300 group-hover:translate-x-1" />
            </Link>
          </motion.div>
        </div>

        <div className="flex overflow-x-auto pb-4 gap-6 lg:gap-8 hide-scrollbar">
          {featuredProducts.map((product, index) => (
            <div className="flex-shrink-0 w-[280px]" key={product.id}>
              <ProductCard product={product} index={index} />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FeaturedProducts;
