
import { useEffect } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { CartProvider } from "@/context/CartContext";
import Navbar from "@/components/Navbar";
import { emailService } from "@/lib/emailService";
import Index from "./pages/Index";
import Products from "./pages/Products";
import ProductDetail from "./pages/ProductDetail";
import Cart from "./pages/Cart";
import Checkout from "./pages/Checkout";
import NotFound from "./pages/NotFound";
import NewsletterAdmin from "./components/NewsletterAdmin";
import EmailTest from "./components/EmailTest";
import { Helmet } from "react-helmet";

const queryClient = new QueryClient();

const App = () => {
  // Initialize EmailJS when the app starts
  useEffect(() => {
    emailService.init();
  }, []);

  return (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <CartProvider>
        <Helmet defaultTitle="Adejola and Sons Enterprise - Premium Artisanal Ice Cream" titleTemplate="%s | Adejola and Sons Enterprise">
          <meta name="description" content="Indulge in premium artisanal ice cream crafted with passion and the finest ingredients by Adejola and Sons Enterprise." />
        </Helmet>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Navbar />
          <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/products" element={<Products />} />
            <Route path="/products/:id" element={<ProductDetail />} />
            <Route path="/cart" element={<Cart />} />
            <Route path="/checkout" element={<Checkout />} />
            <Route path="/newsletter-admin" element={<NewsletterAdmin />} />
            <Route path="/email-test" element={<EmailTest />} />
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </CartProvider>
    </TooltipProvider>
  </QueryClientProvider>
  );
};

export default App;
