
import { useState, useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import { useCart } from "@/context/CartContext";
import { ShoppingCart, Menu, X, Facebook, Instagram, Twitter } from "lucide-react";
import { cn } from "@/lib/utils";
import { color } from "framer-motion";

const Navbar = () => {
  const [scrolled, setScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const { itemCount } = useCart();
  const location = useLocation();

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 20);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  useEffect(() => {
    setMobileMenuOpen(false);
  }, [location.pathname]);

  // Lock body scroll when mobile menu is open
  useEffect(() => {
    if (mobileMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [mobileMenuOpen]);

  const handleNavClick = (link: any) => {
    if (link.isAnchor) {
      // If we're not on the home page, navigate to home first
      if (location.pathname !== '/') {
        window.location.href = `/${link.path}`;
        return;
      }

      // Smooth scroll to section
      const element = document.querySelector(link.path);
      if (element) {
        element.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    }
  };

  const handleHomeClick = () => {
    if (location.pathname === '/') {
      // If already on home page, scroll to top
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    }
  };

  const navLinks = [
    { name: "Home", path: "/" },
    { name: "Products", path: "/products" },
    { name: "About", path: "#about", isAnchor: true },
    { name: "Contact", path: "#contact", isAnchor: true },
  ];

  return (
    <>
      <header
        className={cn(
          "fixed top-0 left-0 w-full z-50 transition-all duration-300",
          scrolled ? "bg-white/90 backdrop-blur-sm shadow-soft py-3" : "bg-transparent py-5"
        )}
      >
        <div className="container-custom flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="flex items-center justify-center w-10 h-10 bg-primary rounded-full">
              <span className="text-white font-bold text-lg">A&S</span>
            </div>
            <Link
              to="/"
              onClick={handleHomeClick}
              className="font-serif text-2xl font-bold text-softBlack hover:opacity-90 transition-opacity"
            >
              Adejola & Sons
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {navLinks.map((link) => (
              link.isAnchor ? (
                <button
                  key={link.name}
                  onClick={() => handleNavClick(link)}
                  className="nav-link"
                >
                  {link.name}
                </button>
              ) : link.name === "Home" ? (
                <Link
                  key={link.name}
                  to={link.path}
                  onClick={handleHomeClick}
                  className="nav-link"
                >
                  {link.name}
                </Link>
              ) : (
                <Link
                  key={link.name}
                  to={link.path}
                  className="nav-link"
                >
                  {link.name}
                </Link>
              )
            ))}

            {/* Social Media Links */}
            <div className="flex items-center space-x-3">
              <a href="https://facebook.com" target="_blank" rel="noopener noreferrer" aria-label="Facebook">
                <Facebook className="h-5 w-5 text-softBlack hover:text-primary transition-colors" />
              </a>
              <a href="https://instagram.com" target="_blank" rel="noopener noreferrer" aria-label="Instagram">
                <Instagram className="h-5 w-5 text-softBlack hover:text-primary transition-colors" />
              </a>
              <a href="https://twitter.com" target="_blank" rel="noopener noreferrer" aria-label="Twitter">
                <Twitter className="h-5 w-5 text-softBlack hover:text-primary transition-colors" />
              </a>
            </div>
          </nav>

          <div className="flex items-center space-x-4">
            <Link to="/cart" className="relative p-2">
              <ShoppingCart className="h-6 w-6 text-softBlack" />
              {itemCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {itemCount}
                </span>
              )}
            </Link>

            {/* Mobile menu button */}
            <button
              className="md:hidden p-2 relative z-[70]"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              aria-label={mobileMenuOpen ? "Close menu" : "Open menu"}
            >
              {mobileMenuOpen ? (
                <X className="h-6 w-6 text-softBlack" />
              ) : (
                <Menu className="h-6 w-6 text-softBlack" />
              )}
            </button>
          </div>
        </div>
      </header>

      {/* Mobile Navigation Backdrop */}
      {mobileMenuOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-[55] md:hidden"
          onClick={() => setMobileMenuOpen(false)}
        />
      )}

      {/* Mobile Navigation */}
      <div
        className={cn(
          "fixed top-0 right-0 h-full w-80 max-w-[85vw] bg-white z-[60] flex flex-col pt-24 px-6 md:hidden transform transition-transform duration-300 ease-in-out shadow-2xl",
          mobileMenuOpen ? "translate-x-0" : "translate-x-full"
        )}
      >
        <nav className="flex flex-col space-y-6 flex-1 overflow-y-auto">
          {navLinks.map((link) => (
            link.isAnchor ? (
              <button
                key={link.name}
                onClick={() => {
                  handleNavClick(link);
                  setMobileMenuOpen(false);
                }}
                className="text-xl font-medium text-softBlack text-left py-2 border-b border-gray-100 hover:text-primary transition-colors"
              >
                {link.name}
              </button>
            ) : link.name === "Home" ? (
              <Link
                key={link.name}
                to={link.path}
                onClick={() => {
                  handleHomeClick();
                  setMobileMenuOpen(false);
                }}
                className="text-xl font-medium text-softBlack py-2 border-b border-gray-100 hover:text-primary transition-colors"
              >
                {link.name}
              </Link>
            ) : (
              <Link
                key={link.name}
                to={link.path}
                className="text-xl font-medium text-softBlack py-2 border-b border-gray-100 hover:text-primary transition-colors"
                onClick={() => setMobileMenuOpen(false)}
              >
                {link.name}
              </Link>
            )
          ))}

          {/* Social Media Links for mobile */}
          <div className="flex items-center space-x-5 pt-8 mt-auto pb-6">
            <a href="https://facebook.com" target="_blank" rel="noopener noreferrer" aria-label="Facebook">
              <Facebook className="h-6 w-6 text-softBlack hover:text-primary transition-colors" />
            </a>
            <a href="https://instagram.com" target="_blank" rel="noopener noreferrer" aria-label="Instagram">
              <Instagram className="h-6 w-6 text-softBlack hover:text-primary transition-colors" />
            </a>
            <a href="https://twitter.com" target="_blank" rel="noopener noreferrer" aria-label="Twitter">
              <Twitter className="h-6 w-6 text-softBlack hover:text-primary transition-colors" />
            </a>
          </div>
        </nav>
      </div>
    </>
  );
};

export default Navbar;
