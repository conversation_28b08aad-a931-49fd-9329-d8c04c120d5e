# Ecommerce Platform

A modern e-commerce platform for Adejola & Sons Enterprise, showcasing their premium artisanal ice cream products.

## 🍦 About

Adejola & Sons Enterprise specializes in distributing premium artisanal ice cream. Our web platform provides a seamless shopping experience for customers to explore and purchase our delicious ice cream products. 🛍️

## 🔗 [Live Demo](https://adejola-global-giant.netlify.app/)

## ⭐ Features

- 🎨 Elegant, responsive design
- 📱 Mobile-optimized interface
- 🔍 Product search and filtering
- 🛒 Real-time shopping cart
- ✨ Smooth animations and transitions
- 📦 Product catalog with categories
- 🔒 Secure checkout process

## 🛠️ Tech Stack

- **Framework:** ⚛️ React 18 with TypeScript
- **Build Tool:** ⚡ Vite
- **Styling:** 
  - 🎭 Tailwind CSS
  - 🎪 Shadcn UI Components
  - 🌟 Custom animations with Framer Motion
- **State Management:** 🔄 React Query
- **Routing:** 🛣️ React Router DOM
- **UI Enhancements:**
  - 🔔 Sonner for toast notifications
  - 🎯 Lucide React for icons
  - 📝 React Hook Form for form handling

## 📦 Getting Started

1. Clone the repository:
```bash
git clone https://github.com/[your-username]/adejola-ice-cream
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

## ⚙️ Available Scripts

- 🚀 `npm run dev` - Launch development server
- 📦 `npm run build` - Create production build
- 🔧 `npm run build:dev` - Create development build
- 👀 `npm run preview` - Preview production build
- 🧹 `npm run lint` - Run ESLint

## 📁 Project Structure

```
src/
├── components/         # 🧩 UI components
│   ├── ui/            # 🎨 Shadcn UI components
│   └── ...            # ✨ Custom components
├── pages/             # 📄 Route pages
├── hooks/             # 🎣 Custom React hooks
├── context/           # 🌍 React Context providers
├── lib/               # 🛠️ Utility functions
└── types/             # 📝 TypeScript definitions
```

## 🚀 Deployment

The site is deployed on Netlify. For deployment:

1. 🔗 Connect your GitHub repository to Netlify
2. ⚙️ Configure build settings:
   - Build command: `npm run build`
   - Publish directory: `dist`
3. 🎉 Deploy!

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request. 🎉

1. 🍴 Fork the repository
2. 🌿 Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. 💾 Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. 📤 Push to the branch (`git push origin feature/AmazingFeature`)
5. 🎯 Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details. ⚖️

## 👨‍💻 Author

**Ayokanmi Adejola**

- Portfolio: [https://ayokanmi-adejola-portfolio.netlify.app/]
- LinkedIn: [https://www.linkedin.com/in/ayokanmiadejola/]
